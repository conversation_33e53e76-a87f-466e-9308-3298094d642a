import { StyleSheet, View, ScrollView } from 'react-native'
import React from 'react'
import Header from '../components/Header'

const Home = () => {
  // Sample user data - you can replace this with real data later
  const userData = {
    name: '<PERSON>',
    avatar: require('../assets/avatar.png'),
    isPro: true,
    notifications: [
      {
        id: '1',
        title: 'Welcome to <PERSON><PERSON><PERSON>!',
        message: 'Start organizing your books today',
        time: '2 hours ago',
        isRead: false,
      },
      {
        id: '2',
        title: 'Book Reminder',
        message: 'You have 3 books to return this week',
        time: '1 day ago',
        isRead: true,
      },
    ],
  }

  // Action button handlers
  const handleRewardsPress = () => {
    console.log('Rewards pressed');
    // Add your rewards navigation/logic here
  };

  const handleReferralPress = () => {
    console.log('Referral pressed');
    // Add your referral navigation/logic here
  };

  return (
    <View style={styles.container}>
      <Header
        userName={userData.name}
        userAvatar={userData.avatar}
        isPro={userData.isPro}
        notifications={userData.notifications}
        onRewardsPress={handleRewardsPress}
        onReferralPress={handleReferralPress}
      />
      <ScrollView style={styles.content}>
        {/* Your main content will go here */}
      </ScrollView>
    </View>
  )
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
  },
})