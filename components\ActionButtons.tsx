import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

interface ActionButtonsProps {
  onRewardsPress?: () => void;
  onReferralPress?: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onRewardsPress,
  onReferralPress,
}) => {
  return (
    <View style={styles.container}>
      {/* Rewards Button */}
      <TouchableOpacity 
        style={styles.actionButton}
        onPress={onRewardsPress}
        activeOpacity={0.7}
      >
        <View style={styles.iconContainer}>
          <View style={styles.giftIcon}>
            <Text style={styles.iconText}>🎁</Text>
          </View>
        </View>
        <Text style={styles.buttonLabel}>Rewards</Text>
      </TouchableOpacity>

      {/* Referral Button */}
      <TouchableOpacity 
        style={styles.actionButton}
        onPress={onReferralPress}
        activeOpacity={0.7}
      >
        <View style={styles.iconContainer}>
          <View style={styles.referralIcon}>
            <Text style={styles.iconText}>👥</Text>
          </View>
        </View>
        <Text style={styles.buttonLabel}>Referral</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 15,
  },
  actionButton: {
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginBottom: 8,
  },
  giftIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  referralIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  iconText: {
    fontSize: 20,
  },
  buttonLabel: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.9,
    fontWeight: '500',
  },
});

export default ActionButtons;
