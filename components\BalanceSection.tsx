import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface BalanceSectionProps {
  totalBalance: number;
  approvedAmount: number;
  pendingAmount: number;
}

const BalanceSection: React.FC<BalanceSectionProps> = ({
  totalBalance,
  approvedAmount,
  pendingAmount,
}) => {
  return (
    <View style={styles.container}>
      {/* Balance Display */}
      <View style={styles.balanceContainer}>
        <Text style={styles.balanceLabel}>Cashback Balance</Text>
        <Text style={styles.balanceAmount}>${totalBalance.toFixed(2)}</Text>
      </View>

      {/* Status Row */}
      <View style={styles.statusContainer}>
        <View style={styles.statusItem}>
          <View style={[styles.statusDot, styles.approvedDot]} />
          <Text style={styles.statusLabel}>Approved</Text>
          <Text style={styles.statusAmount}>${approvedAmount.toFixed(2)}</Text>
        </View>

        <View style={styles.statusItem}>
          <View style={[styles.statusDot, styles.pendingDot]} />
          <Text style={styles.statusLabel}>Pending</Text>
          <Text style={styles.statusAmount}>${pendingAmount.toFixed(2)}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
  },
  balanceContainer: {
    marginBottom: 15,
  },
  balanceLabel: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.8,
    marginBottom: 5,
  },
  balanceAmount: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#ffffff',
    letterSpacing: -1,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  approvedDot: {
    backgroundColor: '#10B981', // Green color for approved
  },
  pendingDot: {
    backgroundColor: '#F59E0B', // Orange/yellow color for pending
  },
  statusLabel: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.8,
    marginRight: 8,
  },
  statusAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default BalanceSection;
