import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Avatar from './Avatar';
import NotificationModal from './NotificationModal';
import NotificationIcon from './NotificationIcon';

interface HeaderProps {
  userName: string;
  userAvatar: any;
  isPro?: boolean;
  notifications?: any[];
}

const Header: React.FC<HeaderProps> = ({
  userName,
  userAvatar,
  isPro = false,
  notifications = [],
}) => {
  const [isNotificationModalVisible, setIsNotificationModalVisible] = useState(false);
  const insets = useSafeAreaInsets();

  const openNotifications = () => {
    setIsNotificationModalVisible(true);
  };

  const closeNotifications = () => {
    setIsNotificationModalVisible(false);
  };

  // Get first name from full name
  const getFirstName = (fullName: string) => {
    return fullName.split(' ')[0];
  };

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={false} />
      <ImageBackground
        source={require('../assets/Header.png')}
        style={[styles.headerBackground, { paddingTop: insets.top + 10 }]}
        resizeMode="stretch"
        imageStyle={styles.backgroundImage}
      >
        <View style={styles.headerContent}>
          <View style={styles.leftSection}>
            <Avatar
              imageSource={userAvatar}
              size={60}
              isPro={isPro}
            />
            <View style={styles.textSection}>
              <Text style={styles.greetingText}>
                Hello {getFirstName(userName)}
              </Text>
              <Text style={styles.welcomeText}>Welcome Back</Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.notificationButton}
            onPress={openNotifications}
            activeOpacity={0.7}
          >
            <View style={styles.bellIcon}>
              <NotificationIcon size={20} color="#ffffff" />
            </View>
            {notifications.length > 0 && (
              <View style={styles.notificationBadge}>
                <Text style={styles.badgeText}>
                  {notifications.length > 9 ? '9+' : notifications.length}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </ImageBackground>

      <NotificationModal
        visible={isNotificationModalVisible}
        onClose={closeNotifications}
        notifications={notifications}
      />
    </>
  );
};

const styles = StyleSheet.create({
  headerBackground: {
    width: '100%',
    paddingBottom: 20,
    paddingHorizontal: 20,
    minHeight: 120,
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 10,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  textSection: {
    marginLeft: 15,
    flex: 1,
  },
  greetingText: {
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 19,
    letterSpacing: -0.12, // -1% of 12px
    color: '#ffffff',
    opacity: 0.8,
    marginBottom: 2,
  },
  welcomeText: {
    fontWeight: '600',
    fontSize: 18,
    lineHeight: 27.9, // 155% of 18px
    letterSpacing: 0, // 0%
    color: '#ffffff',
  },
  notificationButton: {
    position: 'relative',
    padding: 8,
  },
  bellIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },

  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default Header;
