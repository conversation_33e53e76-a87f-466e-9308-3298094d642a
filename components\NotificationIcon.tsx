import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface NotificationIconProps {
  size?: number;
  color?: string;
}

const NotificationIcon: React.FC<NotificationIconProps> = ({
  size = 24,
  color = '#ffffff'
}) => {
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Ionicons
        name="notifications-outline"
        size={size}
        color={color}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NotificationIcon;
